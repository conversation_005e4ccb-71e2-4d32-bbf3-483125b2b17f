@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入自定义样式 */
@import '../styles/table-styles.css';
@import '../styles/single-line-table.css';
@import '../styles/glassmorphism.css';
@import '../styles/login-animations.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 添加新的动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

/* 添加卡片悬停效果 - 移除投影 */
.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-2px);
  border-color: hsl(var(--muted-foreground) / 0.3);
}

.dark .card-hover-effect:hover {
  border-color: hsl(var(--muted-foreground) / 0.4);
}

/* 禁用Shift按下时的文本选择 */
body.shift-select-mode {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  cursor: pointer;
}

/* 在Shift模式下突出显示可选择元素 */
body.shift-select-mode .episode-item {
  transition: transform 0.15s ease-in-out;
}

body.shift-select-mode .episode-item:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* 用户头像下拉菜单层级优化 - 简化z-index，因为日期导航栏已降低到z-10 */
[data-dropdown-menu="true"],
.user-dropdown-menu {
  z-index: 100 !important; /* 简化z-index，高于日期导航栏的z-10即可 */
  isolation: isolate !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  contain: layout style paint !important;
}

/* 侧边栏布局下使用Portal渲染到body */
[data-dropdown-menu="true"][data-layout="sidebar"] {
  position: fixed !important;
  z-index: 100 !important; /* 简化z-index，高于日期导航栏即可 */
  isolation: isolate !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  contain: layout style paint !important;
  pointer-events: auto !important;
}

/* 移动端用户抽屉菜单 */
[data-user-drawer="true"] {
  z-index: 101 !important; /* 略高于下拉菜单 */
  isolation: isolate !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  contain: layout style paint !important;
}

/* 确保Portal渲染的菜单不受其他样式影响 */
body > [data-dropdown-menu="true"] {
  position: fixed !important;
  z-index: 100 !important;
  /* 重置可能影响定位的属性 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  /* 确保渲染层优化 */
  backface-visibility: hidden !important;
  perspective: 1000px !important;
}

/* 外观设置相关样式 */
.compact-mode {
  --spacing-unit: 0.75;
}

.compact-mode .p-4 {
  padding: calc(1rem * var(--spacing-unit));
}

.compact-mode .p-6 {
  padding: calc(1.5rem * var(--spacing-unit));
}

.compact-mode .space-y-4 > * + * {
  margin-top: calc(1rem * var(--spacing-unit));
}

.compact-mode .space-y-6 > * + * {
  margin-top: calc(1.5rem * var(--spacing-unit));
}

.no-animations * {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  transition-duration: 0s !important;
  transition-delay: 0s !important;
}

/* 主色调变量 */
:root[data-primary-color="blue"] {
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
}

:root[data-primary-color="green"] {
  --primary: 142.1 76.2% 36.3%;
  --primary-foreground: 355.7 100% 97.3%;
}

:root[data-primary-color="purple"] {
  --primary: 262.1 83.3% 57.8%;
  --primary-foreground: 210 40% 98%;
}

:root[data-primary-color="red"] {
  --primary: 0 84.2% 60.2%;
  --primary-foreground: 210 40% 98%;
}

:root[data-primary-color="orange"] {
  --primary: 24.6 95% 53.1%;
  --primary-foreground: 210 40% 98%;
}

:root[data-primary-color="pink"] {
  --primary: 330.4 81.2% 60.4%;
  --primary-foreground: 210 40% 98%;
}
