"use client"

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Plus, Calendar, Film, CalendarRange, BarChart2 } from 'lucide-react'
import { WeekdayNavigation } from './weekday-navigation'
import { MediaNewsSection } from './media-news-section'
import { ProgressSection } from './progress-section'
import { WeeklyScheduleSection } from './weekly-schedule-section'
import { UseHomeStateReturn } from '@/hooks/use-home-state'
import { UseMediaNewsReturn } from '@/hooks/use-media-news'
import { LayoutType } from '@/lib/layout-preferences'

interface HomeContentProps {
  homeState: UseHomeStateReturn
  mediaNews: UseMediaNewsReturn
  currentLayout: LayoutType
}

const categories = [
  { id: "all", name: "全部", icon: <LayoutGrid className="h-4 w-4 mr-2" /> },
  { id: "anime", name: "动漫", icon: <Sparkles className="h-4 w-4 mr-2" /> },
  { id: "tv", name: "电视剧", icon: <Tv className="h-4 w-4 mr-2" /> },
  { id: "kids", name: "少儿", icon: <Baby className="h-4 w-4 mr-2" /> },
  { id: "variety", name: "综艺", icon: <Popcorn className="h-4 w-4 mr-2" /> },
  { id: "short", name: "短剧", icon: <Ticket className="h-4 w-4 mr-2" /> },
  { id: "movie", name: "电影", icon: <Clapperboard className="h-4 w-4 mr-2" /> },
]

export function HomeContent({ homeState, mediaNews, currentLayout }: HomeContentProps) {
  const isInSidebar = currentLayout === 'sidebar'
  const containerClasses = isInSidebar 
    ? "mx-auto px-4 sm:px-6 lg:px-8" 
    : "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  return (
    <main className="flex-1">
      <div className={containerClasses}>
        <div className="py-6">
          {/* 主要标签页 */}
          <Tabs value={homeState.activeTab} onValueChange={homeState.setActiveTab}>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <TabsList className="grid w-full sm:w-auto grid-cols-4 lg:grid-cols-4">
                <TabsTrigger value="upcoming" className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span className="hidden sm:inline">即将上线</span>
                </TabsTrigger>
                <TabsTrigger value="recent" className="flex items-center space-x-2">
                  <Film className="h-4 w-4" />
                  <span className="hidden sm:inline">近期开播</span>
                </TabsTrigger>
                <TabsTrigger value="weekly" className="flex items-center space-x-2">
                  <CalendarRange className="h-4 w-4" />
                  <span className="hidden sm:inline">每周放送</span>
                </TabsTrigger>
                <TabsTrigger value="progress" className="flex items-center space-x-2">
                  <BarChart2 className="h-4 w-4" />
                  <span className="hidden sm:inline">追剧进度</span>
                </TabsTrigger>
              </TabsList>

              {/* 右侧操作区 */}
              <div className="flex items-center space-x-3">
                {/* 分类筛选（仅在进度页面显示） */}
                {(homeState.activeTab === 'weekly' || homeState.activeTab === 'progress') && (
                  <Select 
                    value={homeState.selectedCategory} 
                    onValueChange={homeState.setSelectedCategory}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center">
                            {category.icon}
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                {/* 添加按钮 */}
                <Button
                  onClick={() => homeState.setShowAddDialog(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span className="hidden sm:inline">添加词条</span>
                </Button>
              </div>
            </div>

            {/* 标签页内容 */}
            <TabsContent value="upcoming" className="space-y-6">
              <MediaNewsSection
                type="upcoming"
                title="即将上线"
                items={mediaNews.upcomingItems}
                loading={mediaNews.loadingUpcoming}
                error={mediaNews.upcomingError}
                lastUpdated={mediaNews.upcomingLastUpdated}
                isMissingApiKey={mediaNews.isMissingApiKey}
                selectedRegion={homeState.selectedRegion}
                onRegionChange={homeState.setSelectedRegion}
                onRefresh={() => mediaNews.fetchUpcomingItems(homeState.selectedRegion, false)}
                onShowSettings={() => homeState.setShowSettingsDialog(true)}
              />
            </TabsContent>

            <TabsContent value="recent" className="space-y-6">
              <MediaNewsSection
                type="recent"
                title="近期开播"
                items={mediaNews.recentItems}
                loading={mediaNews.loadingRecent}
                error={mediaNews.recentError}
                lastUpdated={mediaNews.recentLastUpdated}
                isMissingApiKey={mediaNews.isMissingApiKey}
                selectedRegion={homeState.selectedRegion}
                onRegionChange={homeState.setSelectedRegion}
                onRefresh={() => mediaNews.fetchRecentItems(homeState.selectedRegion, false)}
                onShowSettings={() => homeState.setShowSettingsDialog(true)}
              />
            </TabsContent>

            <TabsContent value="weekly" className="space-y-6">
              <WeeklyScheduleSection
                homeState={homeState}
                currentLayout={currentLayout}
                categories={categories}
              />
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              <ProgressSection
                homeState={homeState}
                currentLayout={currentLayout}
                categories={categories}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}