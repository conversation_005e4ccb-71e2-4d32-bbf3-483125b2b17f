# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# TMDB-Import directory (external tool)
TMDB-Import-master/

# AI Assistant directories
.augment/
.kiro/

# Local data files
data/

# Prisma generated files
lib/generated/
prisma/dev.db
prisma/migrations/
