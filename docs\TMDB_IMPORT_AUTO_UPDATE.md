# TMDB-Import 工具管理功能

## 功能概述

TMDB Helper 提供了完整的 TMDB-Import 工具管理功能，采用紧凑统一的界面设计，简化了工具的安装、配置和维护流程。系统支持一键自动下载安装，并自动配置工具路径。

## 主要特性

### 🔄 自动版本检测
- 实时检查 GitHub 上的最新版本
- 使用 Git 提交信息作为版本标识
- 对比本地版本与远程版本
- 智能判断是否需要更新

### 📦 一键自动安装
- 自动下载最新源码压缩包到项目根目录
- 解压为 `TMDB-Import-master` 目录
- 自动识别并配置工具路径
- 保存版本信息便于后续管理

### 🎨 紧凑统一界面
- 整合所有功能到单一卡片组件
- 网格布局显示状态和版本信息
- 适中尺寸的操作按钮
- 清晰的信息层次结构

### 🛠️ 智能路径管理
- 自动路径识别和配置
- 保留手动路径设置选项
- 实时状态显示和更新

## 使用方法

### 1. 访问设置页面
1. 打开 TMDB Helper 应用
2. 点击右上角的设置按钮
3. 选择"工具设置"标签页

### 2. 使用工具管理
1. 在"TMDB-Import 工具管理"卡片中查看当前状态
2. 点击"刷新"按钮检查最新版本和安装状态
3. 如果显示"需要更新"，点击"下载安装"或"更新版本"
4. 系统自动下载、解压并配置路径

### 3. 手动路径配置（可选）
如果您已有 TMDB-Import 安装，可以：
1. 在"手动指定工具路径"区域输入路径
2. 点击文件夹图标选择路径
3. 保存设置

## 技术实现

### 后端 API
- **路径**: `/api/tmdb-import-updater`
- **功能**: 
  - `GET ?action=check`: 检查版本信息
  - `GET ?action=status`: 获取安装状态
  - `POST {action: 'download'}`: 下载最新版本
  - `POST {action: 'install'}`: 安装更新

### 前端组件
- **组件**: `TMDBImportUpdater`
- **位置**: `components/tmdb-import-updater.tsx`
- **集成**: 嵌入到设置对话框中

### 安装目录
- **默认路径**: `./tools/tmdb-import/`
- **版本文件**: `.version` (JSON格式)
- **备份策略**: 自动备份到 `tmdb-import-backup-{timestamp}`

## 版本信息格式

系统会在安装目录创建 `.version` 文件，包含以下信息：

```json
{
  "commitSha": "abc123...",
  "commitDate": "2024-01-01T12:00:00Z",
  "commitMessage": "Fix: 修复某个问题",
  "installDate": "2024-01-01T12:30:00Z"
}
```

## 错误处理

### 常见问题及解决方案

1. **网络连接失败**
   - 检查网络连接
   - 确认可以访问 GitHub
   - 重试操作

2. **权限不足**
   - 确保应用有写入权限
   - 检查目标目录权限
   - 以管理员身份运行（如需要）

3. **解压失败**
   - 检查磁盘空间
   - 确认系统支持解压操作
   - 手动删除临时文件后重试

4. **版本检测失败**
   - 检查 GitHub API 访问
   - 确认仓库地址正确
   - 稍后重试

## 兼容性说明

### 支持的平台
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu 18.04+)

### 系统要求
- Node.js 18+
- 网络连接
- 足够的磁盘空间 (约 50MB)

### 与现有安装的兼容性
- 自动检测现有安装
- 保留配置文件
- 支持从手动安装迁移到自动管理

## 安全考虑

### 数据安全
- 自动备份现有安装
- 版本信息加密存储
- 下载文件完整性验证

### 网络安全
- 使用 HTTPS 连接
- 验证下载源
- 限制访问权限

## 故障排除

### 日志查看
系统会在控制台输出详细日志，包括：
- 下载进度
- 安装步骤
- 错误信息
- 版本对比结果

### 手动恢复
如果自动更新失败，可以：
1. 从备份目录恢复
2. 手动下载并解压
3. 重新配置路径

### 重置安装
如果需要完全重新安装：
1. 删除 `./tools/tmdb-import/` 目录
2. 清除本地存储中的路径配置
3. 重新执行自动安装

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持自动版本检测
- 支持一键安装更新
- 集成到设置页面

## 反馈与支持

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看本文档的故障排除部分
2. 在 GitHub 仓库提交 Issue
3. 提供详细的错误信息和系统环境

---

**注意**: 此功能需要网络连接才能正常工作。首次使用时建议在网络状况良好的环境下进行。
