/* TMDB Helper 登录页面动画样式 */

/* 电影胶片孔洞动画 */
@keyframes filmHole {
  0%, 100% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.film-hole {
  animation: filmHole 3s ease-in-out infinite;
}

.film-hole:nth-child(2) {
  animation-delay: 0.5s;
}

.film-hole:nth-child(3) {
  animation-delay: 1s;
}

.film-hole:nth-child(4) {
  animation-delay: 1.5s;
}

.film-hole:nth-child(5) {
  animation-delay: 2s;
}

.film-hole:nth-child(6) {
  animation-delay: 2.5s;
}

/* 数据流动动画 */
@keyframes dataFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.data-flow {
  animation: dataFlow 4s ease-in-out infinite;
}

.data-flow:nth-child(2) {
  animation-delay: 1.5s;
}

.data-flow:nth-child(3) {
  animation-delay: 3s;
}

/* 卡片悬浮效果 */
@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.card-float {
  animation: cardFloat 6s ease-in-out infinite;
}

/* 渐变文字动画 */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 功能卡片悬停效果 */
.feature-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* 输入框聚焦效果 */
.input-group {
  position: relative;
}

/* 输入框图标样式 - 抵消backdrop-blur影响 */
.input-group .lucide {
  filter: none !important;
  backdrop-filter: none !important;
  isolation: isolate;
  z-index: 10;
  position: relative;
}

.input-group:focus-within .lucide {
  color: #3b82f6;
}

.input-group:hover .lucide {
  color: #6b7280;
}

.input-group input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮悬停效果 */
.login-button {
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

/* 背景粒子效果 */
@keyframes particle {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: particle 15s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; }
.particle:nth-child(9) { left: 90%; animation-delay: 16s; }

/* 响应式调整 */
@media (max-width: 1024px) {
  .feature-card {
    padding: 1rem;
  }
  
  .gradient-text {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .card-float {
    animation: none;
  }
  
  .particle {
    display: none;
  }
}

/* 图标样式已完全移除，使用Lucide React默认样式 */

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .feature-card {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .feature-card:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* 深色模式下图标保持默认样式 */
}
